import json
import pyJianYingDraft as draft
from pyJianYingDraft.exceptions import SegmentOverlap
from keyframe import process_keyframes
from text import text_style
from track import get_or_create_track
from audio import add_audio_style

def process_single_template(script, text, template_name, duration, start, template_config_path="new_config.json"):
    """
    处理单个字幕模板
    
    Args:
        script: ScriptFile对象
        text: 要显示的文本
        template_name: 模板名称 (如 "基础字幕", "转折句", "介绍句")
        duration: 持续时间（微秒）
        start: 开始时间（微秒）
        template_config_path: 配置文件路径，默认为new_config.json
    """
    # 加载配置文件
    with open(template_config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查模板是否存在
    if "text_and_audio" not in config or template_name not in config["text_and_audio"]:
        print(f"警告：模板 '{template_name}' 不存在于配置文件中，使用基础字幕模板")
        template_name = "基础字幕"
        if template_name not in config["text_and_audio"]:
            print("错误：基础字幕模板也不存在")
            return []
    
    template = config["text_and_audio"][template_name]
    
    # 检查是否为组合模板（数组形式）
    if isinstance(template, list):
        print(f"错误：'{template_name}' 是组合模板，应该使用 process_combined_template")
        return []
    
    # 只读取"text"，假设所有模板都有嵌套"text"对象
    text_config = template["text"]
    style = text_config.get("style", {})
    
    # 创建时间范围
    time_range = (start, start + duration)
    
    # 创建文本段
    segment_dict = {
        "text": text,
        "time_range": time_range,
        "font": style.get("font"),
        "style": style,
        "intro_animation": text_config.get("intro_animation", {"type": "null", "duration": 0})
    }
    
    text_segment = text_style(segment_dict)
    
    # 确定轨道名称
    track_name = text_config.get("track", "text_track_0")
    
    # 获取或创建轨道
    get_or_create_track(script, draft.TrackType.text, track_name)
    
    # 添加文本段到轨道
    print(f"添加文本段到轨道 '{track_name}': 开始={start/1000000:.3f}s, 持续={duration/1000000:.3f}s")
    
    try:
        script.add_segment(text_segment, track_name)
    except SegmentOverlap as e:
        print(f"错误：文本段重叠，跳过添加: {e}")
        print(f"  字幕内容: {text[:30]}...")
    
    # 处理音效
    if "audio" in text_config:
        audio_config = text_config["audio"]
        audio_name = audio_config.get("name")
        audio_location = audio_config.get("location", "start")
        
        if audio_name:
            audio_segment = add_audio_style(
                audio_name,
                start,
                start + duration,
                audio_location
            )
            if audio_segment:
                audio_track = "audio_track_0"
                get_or_create_track(script, draft.TrackType.audio, audio_track)
                try:
                    script.add_segment(audio_segment, audio_track)
                except SegmentOverlap as e:
                    print(f"警告：音效段重叠，跳过添加: {e}")
    
    segments = [{
        "segment": text_segment,
        "track": track_name,
        "template": text_config,
        "index": 0
    }]
    
    # 处理关键帧
    if "keyframe" in text_config:
        process_keyframes(segments)
    
    return segments


def process_combined_template(script, segments_list, template_name, group_id, template_config_path="new_config.json"):
    """
    处理组合字幕模板
    
    Args:
        script: ScriptFile对象
        segments_list: 组合片段列表，每个包含:
            - text: 文本内容
            - start: 开始时间（微秒）
            - duration: 持续时间（微秒）
            - index_in_group: 在组内的索引(1-based)
        template_name: 模板名称 (如 "开头钩子")
        group_id: 组的唯一标识符，用于区分不同的组
        template_config_path: 配置文件路径
    """
    # 加载配置文件
    with open(template_config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查模板是否存在
    if "text_and_audio" not in config or template_name not in config["text_and_audio"]:
        print(f"警告：组 {group_id} 的模板 '{template_name}' 不存在于配置文件中")
        return []
    
    template = config["text_and_audio"][template_name]
    
    # 确保是组合模板（列表形式）
    if not isinstance(template, list):
        print(f"错误：组 {group_id} 的 '{template_name}' 不是组合模板")
        return []
    
    segments = []
    segment_refs = []  # 用于处理segment_end引用
    
    # 为每个字幕应用对应的模板段
    for segment_info in segments_list:
        index_in_group = segment_info.get("index_in_group", 1) - 1  # 转换为0-based索引
        
        if index_in_group >= len(template):
            print(f"警告：组 {group_id} 中索引 {index_in_group + 1} 超出模板 '{template_name}' 的范围")
            continue
        
        sub_template = template[index_in_group]
        text = segment_info["text"]
        start = segment_info["start"]
        duration = segment_info["duration"]
        
        print(f"[调试] 处理组合模板段 (group_id={group_id}): index={index_in_group}, text={text[:20]}...")
        
        # 获取文本配置
        text_config = sub_template.get("text", {})
        style = text_config.get("style", {})
        
        # 创建时间范围
        time_range = (start, start + duration)
        
        # 保存引用信息，稍后处理segment_end
        segment_refs.append({
            "segment_info": segment_info,
            "sub_template": sub_template,
            "text_config": text_config,
            "style": style,
            "time_range": time_range,
            "index": index_in_group
        })
    
    # 处理segment_end引用
    for i, ref in enumerate(segment_refs):
        text_config = ref["text_config"]
        
        # 检查是否有segment_end引用
        if "segment_end" in text_config:
            ref_index = text_config["segment_end"]
            if ref_index < len(segment_refs):
                # 获取引用段的结束时间
                ref_segment = segment_refs[ref_index]
                ref_end_time = ref_segment["time_range"][1]
                
                # 更新当前段的时间范围
                start_time = ref["time_range"][0]
                ref["time_range"] = (start_time, ref_end_time)
                
                print(f"  组 {group_id} 处理segment_end: 段{i}引用段{ref_index}, 新时间范围: {start_time/1000000:.3f}s - {ref_end_time/1000000:.3f}s")
    
    # 创建文本段并添加到轨道
    for ref in segment_refs:
        segment_dict = {
            "text": ref["segment_info"]["text"],
            "time_range": ref["time_range"],
            "font": ref["style"].get("font"),
            "style": ref["style"],
            "intro_animation": ref["text_config"].get("intro_animation", {"type": "null", "duration": 0})
        }
        
        text_segment = text_style(segment_dict)
        
        # 确定轨道名称
        track_index = ref["text_config"].get("track", ref["index"])
        track_name = f"text_track_{track_index}"
        
        # 获取或创建轨道
        get_or_create_track(script, draft.TrackType.text, track_name)
        
        # 添加文本段到轨道
        try:
            script.add_segment(text_segment, track_name)
            print(f"  添加到轨道 '{track_name}': {ref['segment_info']['text'][:20]}...")
        except SegmentOverlap as e:
            print(f"  错误：文本段重叠，跳过添加: {e}")
        
        # 处理音效
        if "audio" in ref["sub_template"]:
            audio_config = ref["sub_template"]["audio"]
            audio_name = audio_config.get("name")
            audio_location = audio_config.get("location", "start")
            
            if audio_name:
                audio_segment = add_audio_style(
                    audio_name,
                    ref["time_range"][0],
                    ref["time_range"][1],
                    audio_location
                )
                if audio_segment:
                    audio_track = f"audio_track_{ref['index']}"
                    get_or_create_track(script, draft.TrackType.audio, audio_track)
                    try:
                        script.add_segment(audio_segment, audio_track)
                    except SegmentOverlap as e:
                        print(f"  警告：音效段重叠，跳过添加: {e}")
        
        segments.append({
            "segment": text_segment,
            "track": track_name,
            "template": ref["text_config"],
            "index": ref["index"],
            "segment_info": ref["segment_info"]
        })
    
    # 处理关键帧
    for seg in segments:
        if "keyframe" in seg["template"]:
            process_keyframes([seg])
    
    return segments

def single_video_template(script, single_segment, template_config, template_name):
    """
    对单个视频片段应用模板
    """

    # 获取到视频的开始时间和持续时间
    
    # 添加背景

    # 添加特效

    # 添加视频片段

    # 添加关键帧

    # 



def add_fixed_template(script, template_config, text, video_duration, template_name="文案标题"):
    """
    添加固定模板（如文案标题），该模板会在整个视频期间显示
    
    Args:
        script: ScriptFile对象
        template_config: 从script_template_1.json加载的配置
        text: 要显示的文本内容
        video_duration: 视频总时长（微秒）
        template_name: 模板名称，默认为"文案标题"
    """
    if template_name not in template_config:
        print(f"警告：固定模板 '{template_name}' 不存在")
        return None
    
    template_data = template_config[template_name]
    
    # 处理模板结构
    if isinstance(template_data, dict) and "template" in template_data:
        template = template_data["template"]
    else:
        template = template_data
    
    # 确保模板包含text配置
    if "text" not in template:
        print(f"错误：模板 '{template_name}' 缺少text配置")
        return None
    
    text_config = template["text"]
    
    # 计算起始时间
    # 支持start_frame（帧偏移）或start_time（时间偏移）
    if "start_frame" in text_config:
        # 1帧 = 33333微秒（30fps）
        frame_duration = 33333
        start_time = text_config["start_frame"] * frame_duration
        print(f"  使用start_frame={text_config['start_frame']}，起始时间={start_time/1000000:.3f}秒")
    else:
        # 使用start_time，默认为0
        start_time = text_config.get("start_time", 0)
    
    # 处理duration字段
    duration_setting = text_config.get("duration", "video_end")
    if duration_setting == "video_end":
        duration = video_duration - start_time
    else:
        duration = duration_setting
    
    # 创建文本段
    segment_dict = {
        "text": text,
        "time_range": (start_time, start_time + duration),
        "font": text_config.get("font"),
        "style": text_config.get("style"),
        "intro_animation": text_config.get("intro_animation", {"type": "null", "duration": 0})
    }
    
    text_segment = text_style(segment_dict)
    
    # 获取轨道索引
    track_index = text_config.get("track", 2)
    track_name = f"字幕{track_index}"
    
    # 获取或创建轨道
    get_or_create_track(script, draft.TrackType.text, track_name)
    
    # 添加文本段到轨道
    print(f"添加固定模板 '{template_name}' 到轨道 '{track_name}': 文本='{text}', 开始={start_time/1000000:.2f}s, 持续={duration/1000000:.2f}s")
    
    try:
        script.add_segment(text_segment, track_name)
        return text_segment
    except SegmentOverlap as e:
        print(f"错误：固定模板段重叠，跳过添加: {e}")
        return None
