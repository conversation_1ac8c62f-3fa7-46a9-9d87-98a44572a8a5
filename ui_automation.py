import uiautomation as auto
import time
import pyJianYingDraft as draft
from config import BASE_DRAFT_PATH, BASE_JIANYING_PATH
import threading
import subprocess

# 全局变量存储初始化器
_thread_initializers = {}

def _ensure_com_initialized():
    """确保当前线程的COM已初始化"""
    thread_id = threading.get_ident()
    if thread_id not in _thread_initializers:
        # 为当前线程创建初始化器
        _thread_initializers[thread_id] = auto.UIAutomationInitializerInThread()

def create_draft_with_video(article_name: str, video_path: str, cover_image_path: str = None):

    draft_folder = draft.DraftFolder(BASE_DRAFT_PATH)

    draft_name = article_name
    script = draft_folder.create_draft(draft_name, 1080, 1920)  
    video_material = draft.VideoMaterial(video_path)

    script.add_track(draft.TrackType.video)

    # 计算起始位置
    start_time = 0
    
    # 如果有封面图片，先添加封面
    if cover_image_path:
        import os
        if os.path.exists(cover_image_path):
            print(f"添加封面图片: {cover_image_path}")
            # 使用VideoMaterial来处理图片
            cover_material = draft.VideoMaterial(cover_image_path)
            script.add_material(cover_material)
            
            # 创建图片段，时长为1帧（约33333微秒，30fps）
            frame_duration = 33333  # 1帧的时长（微秒）
            cover_segment = draft.VideoSegment(
                cover_image_path,
                draft.trange(0, frame_duration)
            )
            script.add_segment(cover_segment)
            
            # 视频起始时间向后移动1帧
            start_time = frame_duration
        else:
            print(f"警告：封面图片不存在: {cover_image_path}")
    
    # 添加视频段，起始时间根据是否有封面调整
    video_segment = draft.VideoSegment(
        video_path,  
        draft.trange(start_time, video_material.duration)  # 第二个参数是持续时间，不是结束时间
    )

    script.add_segment(video_segment)
    script.save()

# 按照控件相对位置来点击控件，支持额外的筛选条件
def click_child_button(parent, prefix, index, condition=None):
    """专门点击指定的控件，支持额外的筛选条件"""
    count = 0
    for control in parent.GetChildren():
        if control.ClassName and control.ClassName.startswith(prefix):
            # 如果提供了额外条件，检查是否满足
            if condition is None or condition(control):
                count += 1
                if count == index:
                    control.Click()
                    print(f"Clicked the control: {control.ClassName}")
                    return True
    print(f"Control not found (total found: {count})")
    return False

# 定义筛选条件：控件没有子控件
def no_children_condition(control):
    """检查控件是否没有子控件"""
    children = control.GetChildren()
    return not children

def article_sync_video_workflow(article_name, video_path, article, cover_image_path=None):
    # 确保COM在当前线程初始化
    _ensure_com_initialized()

    create_draft_with_video(article_name, video_path, cover_image_path)

    jianying_start = auto.WindowControl(searchDepth=1, SubName="剪映专业版")

    jianying_start.SetActive()

    title_buttons = []
    for i in range(1, 4):  # 假设最多有10个按钮
        try:
            button = jianying_start.Control(searchDepth=1, ClassName="TitleBarButton", foundIndex=i)
            if button.Exists(0, 0):  # 检查控件是否存在
                title_buttons.append(button)
            else:
                break
        except Exception as e:
            print(e)
            print(f"未找到第{i}个TitleBarButton控件")
            break

    # 找出x坐标最大的按钮并点击
    if title_buttons:
        max_x_button = max(title_buttons, key=lambda btn: btn.BoundingRectangle.left)
        max_x_button.Click()

    time.sleep(5)

    # 打开剪映5.9
    subprocess.Popen(BASE_JIANYING_PATH)


    time.sleep(12)

    jianying_start = auto.WindowControl(searchDepth=1, SubName="剪映专业版")

    jianying_start.SetActive()

    # 获取搜索控件，并查询草稿文件名
    click_child_button(jianying_start, "QUIButton_QMLTYPE", 2)

    auto.SetClipboardText(article_name)  # 设置剪贴板内容
    jianying_start.SendKeys("{Ctrl}a")
    jianying_start.SendKeys("{Ctrl}v")

    time.sleep(0.5)

    jianying_start.SendKeys("{Enter}")

    click_child_button(jianying_start, "HomePageOpenProjectItem_QMLTYPE", 1)


    # 进入编辑窗口，需要重新获取新窗口对象

    jianying_build = auto.WindowControl(searchDepth=1, SubName="剪映专业版")

    jianying_build.BoundingRectangle

    # 文本坐标，这个没有办法解决，只能用相对坐标
    relative_x_text = 153  # 相对于窗口的x坐标
    relative_y_text = 53    # 相对于窗口的y坐标

    jianying_build.Click(relative_x_text, relative_y_text)


    ########################################
    ##智能字幕坐标，控件相对位置定位方式，更稳定##
    #######################################

    # 使用click_child_button来点击y值第二高的QQuickText控件
    # 先获取总数来判断应该使用的索引
    click_child_button(jianying_build, "QQuickText", 7, condition=no_children_condition)

    ### 开始匹配坐标，相对坐标方式，不稳定
    ### relative_x_start_match = 360
    ### relative_y_start_match = 250

    ### jianying_build.Click(relative_x_start_match, relative_y_start_match)

    ###########################
    ##控件相对位置方式，更稳定####
    ##########################

    # 直接调用
    click_child_button(jianying_build, "LVButton_QMLTYPE", 2)



    # 输入文稿内容
    text_content = article


    jianying_input_text = auto.WindowControl(searchDepth=2, SubName="输入文稿")

    # window_rect_input_text = jianying_input_text.BoundingRectangle

    # 输入文稿坐标
    # relative_x_input_text = 725 - 690
    # relative_y_input_text = 250 - 156

    # jianying_input_text.Click(relative_x_input_text, relative_y_input_text)

    click_child_button(jianying_input_text, "TextArea_QMLTYPE", 1)

    auto.SetClipboardText(text_content)  # 设置剪贴板内容

    jianying_input_text.SendKeys("{Ctrl}v")

    # 点击开始匹配
    relative_x_input_text_start_match = 1100 - 690
    relative_y_input_text_start_match = 876 - 180

    jianying_input_text.Click(relative_x_input_text_start_match, relative_y_input_text_start_match)

    time.sleep(20)

    # 关闭按扭
    # ratio_x_close = 0.993
    # ratio_y_close = 0.018

    # jianying_build.Click(ratioX=ratio_x_close, ratioY=ratio_y_close)
    # 获取所有TitleBarButton控件
    title_buttons = []
    for i in range(1, 4):  # 假设最多有10个按钮
        try:
            button = jianying_build.Control(searchDepth=1, ClassName="TitleBarButton", foundIndex=i)
            if button.Exists(0, 0):  # 检查控件是否存在
                title_buttons.append(button)
            else:
                break
        except Exception as e:
            print(e)
            print(f"未找到第{i}个TitleBarButton控件")
            break

    # 找出x坐标最大的按钮并点击
    if title_buttons:
        max_x_button = max(title_buttons, key=lambda btn: btn.BoundingRectangle.left)
        max_x_button.Click()

    