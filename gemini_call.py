from google import genai
from google.genai import types
import dotenv
import os
from contextlib import contextmanager

# 加载环境变量
dotenv.load_dotenv()

@contextmanager
def _temporary_proxy_env_from_user_vars():
    """
    在调用期间，将环境变量 `httpproxy`/`httpsproxy` 应用于标准的代理变量
    `HTTP_PROXY`/`HTTPS_PROXY`（同时设置大小写版本），调用结束后恢复/清理。
    """
    http_proxy_value = os.getenv("HTTP_PROXY")
    https_proxy_value = os.getenv("HTTPS_PROXY")

    previous_values = {
        "HTTP_PROXY": os.environ.get("HTTP_PROXY"),
        "http_proxy": os.environ.get("http_proxy"),
        "HTTPS_PROXY": os.environ.get("HTTPS_PROXY"),
        "https_proxy": os.environ.get("https_proxy"),
    }

    try:
        # 应用新的代理设置（无值则确保清除）
        for name, value in (
            ("HTTP_PROXY", http_proxy_value),
            ("http_proxy", http_proxy_value),
            ("HTTPS_PROXY", https_proxy_value),
            ("https_proxy", https_proxy_value),
        ):
            if value:
                os.environ[name] = value
            else:
                os.environ.pop(name, None)
        yield
    finally:
        # 恢复之前的环境（若之前不存在则清理）
        for name, old_value in previous_values.items():
            if old_value is None:
                os.environ.pop(name, None)
            else:
                os.environ[name] = old_value

def generate_gemini_response_json(prompt, response_schema, model="gemini-2.5-pro"):
    # 创建客户端 - 会自动从GEMINI_API_KEY环境变量读取API密钥
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    with _temporary_proxy_env_from_user_vars():
        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )
        response = client.models.generate_content(
            model=model,
            contents=prompt,
            config=types.GenerateContentConfig(
                response_mime_type="application/json",
                response_schema=response_schema
            )
        )
        return response

def generate_gemini_response(prompt, model="gemini-2.5-pro"):
    # 创建客户端 - 会自动从GEMINI_API_KEY环境变量读取API密钥
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    with _temporary_proxy_env_from_user_vars():
        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )
        response = client.models.generate_content(
            model=model,
            contents=prompt,
            config=types.GenerateContentConfig(
                response_mime_type="text/plain"
            )
        )
        return response