import json
import pyJianYingDraft as draft
from config import BASE_DRAFT_PATH
from text import read_subtitles_from_draft
from track import get_or_create_track
from video import read_cover_from_draft
from template import process_combined_template, process_single_template, process_video_template, add_fixed_template

def process_article_subtitles(draft_path, article_name, article_config: list[dict], 
                                template_config_path="script_template_1.json",
                                video_paths=None, initial_video_index=0, canvas_width=1080, canvas_height=1920,
                                script_title=None):
    """
    
    参数:
    draft_path: 原始草稿文件路径（用于读取字幕时间）
    output_path: 输出草稿文件路径
    article_config_path: new_text.json配置文件路径
    template_config_path: 模板配置文件路径
    video_paths: 视频文件路径列表，如果提供则处理视频剪辑
    initial_video_index: 初始使用的视频索引（0或1）
    script_title: 文案标题文本，如果提供则显示在视频上
    """
    # 加载配置文件
    article_segments = article_config
    
    with open(template_config_path, 'r', encoding='utf-8') as f:
        template_config = json.load(f)
    
    # 从原始草稿读取信息
    subtitles = read_subtitles_from_draft(draft_path)
    cover = read_cover_from_draft(draft_path)

    # 创建新草稿
    draft_folder = draft.DraftFolder(BASE_DRAFT_PATH)
    
    script = draft_folder.create_draft(article_name, canvas_width, canvas_height)

    # 预先创建好所需要的轨道（如模板配置中提供了track清单）
    if isinstance(template_config, dict) and "track" in template_config:
        track_type_map = {
            "text": draft.TrackType.text,
            "video": draft.TrackType.video,
            "audio": draft.TrackType.audio,
            "effect": draft.TrackType.effect,
        }
        for track_type_key, track_names in template_config["track"].items():
            track_type = track_type_map.get(track_type_key, draft.TrackType.text)
            for track_name in track_names:
                get_or_create_track(script, track_type, track_name)

    # 加载原始草稿的文本和时间信息，将打好标签的文本应用到对应的片段中去
    # 通过索引匹配来处理可能的重复文本情况
    for i, segment in enumerate(article_segments):
        if i < len(subtitles):
            segment['start'] = subtitles[i]['start']
            segment['duration'] = subtitles[i]['duration']
        else:
            # 处理字幕数量不足的情况
            print(f"Warning: No subtitle timing for segment {i}: {segment['text'][:30]}...")
            segment['start'] = 0
            segment['duration'] = 0

    # 1. 先区分出单独片段和组合片段
    single_segements = []
    combined_segements = []
    for segment in article_segments:
        # 修复逻辑：先检查是否存在group_id，再检查数值
        if segment.get("group_id") is None or segment["group_id"] <= 0:
            single_segements.append(segment)
        else:
            combined_segements.append(segment)

    # 2. 应用单独片段模板（只处理有时间信息的片段）
    for segment in single_segements:
        if segment.get('duration', 0) > 0:  # 确保有有效的时间信息
            process_single_template(script, segment["text"], segment["type"], segment["duration"], segment["start"])

    # 3. 应用组合片段模板（只处理有时间信息的片段）
    for segment in combined_segements:
        if segment.get('duration', 0) > 0:  # 确保有有效的时间信息
            process_combined_template(script, segment["text"], segment["type"], segment["duration"], segment["start"])


def process_video_segment(script, article_segments, template_config, script_title):
    """
    应用视频模板
    """
    # 根据是否存在video_type和video_point来判断是否需要应用视频模板
    # start 到 end 为一个片段
    for segment in article_segments:
        if segment["video_type"] and segment["video_point"]:
            process_video_template(script, segment["text"], segment["type"])
    
    # 5. 应用固定模板
    add_fixed_template(script, template_config, script_title)