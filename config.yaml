first_clip:                                     # ====== 第一段 ======
  duration_frames: 200                          # 例：整段共 200 帧，给脚本做边界校验
  tracks:
    # ---------- 1. 黑色背景图片 ----------
    black_bg:
      type: image                               # 静态图
      path: assets/black.png
      track: video_track_2
      keyframes:
        - frame: 0
          style:
            alpha: 0.0
        - frame: 20
          style:
            alpha: 0.80
        - frame: -0                             # “倒数第0帧”=最后一帧
          style:
            alpha: 0.0
        - frame: -20
          style:
            alpha: 0.80

    # ---------- 2. 原视频 ----------
    original_video:
      type: video
      track: video_track_0
      keyframes:
        - frame: 0
          style: { scale_x: 1.00, scale_y: 1.00}
        - frame: 3
          style: { scale_x: 1.20, scale_y: 1.20}
        - frame: -10
          style: { scale_x: 1.30, scale_y: 1.30}

    # ---------- 3. 模板特效 ----------
    template_fx:
      type: effect
      effect_name: 模糊
      params: { blur_percent: 0.50 }
      track: video_track_1
    # ---------- 4. 原视频 + 矩形蒙版 ----------
    masked_video:
      inherit_from: original_video              # 复用同一素材
      track: video_track_3
      mask:
        shape: rectangle
        position: [26, 173]                     # x, y
        size: [700, 900]                       # w, h
        feather: 5
        corner_radius: 20
        y_sign_rule:                            # 你的备注规则
          description: |
            当 y < 0 时，字幕位置为正数；如 -900 → 900，-1000 → 1000。
            反之则保持负数

  # ---------- 5. 变化序列 ----------
  variations:
    # 变化1：从下往上
    - order: 1
      elements:
        - original_video
        - template_fx
        - black_bg
        - masked_video
      note: 从下往上叠放；后续脚本按顺序放入对应轨道

  # ---------- 6. 连续引用画面特殊处理 ----------
  follow_ups:
    apply_on_next_3_clips: true                 # 连续三引用画面触发
    keyframes:
      - frame: 0
        style: { scale: 1.00 }
      - frame: 0
        style: { scale: 1.20 }
      - frame: 0
        style: { scale: 1.30 }

# ------------- 其余段落继续追加 -------------